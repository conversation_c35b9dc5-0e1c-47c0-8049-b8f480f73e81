import { electronAPI } from '@electron-toolkit/preload'
import { IpcRendererEvent, contextBridge, ipcRenderer } from 'electron'
import {
	CompressOptions,
	ExtractAudioOptions,
	MainProcessNoticeType,
	VideoConvertOptions,
	VideoCompressOptions,
	ExtractFramesOptions,
	ImageCompressOptions,
	ImageConvertOptions,
	ImageEnhanceOptions
} from '../renderer/src/types'

// Custom APIs for renderer
const api = {
	// 旧版压缩视频
	compress: (options: CompressOptions) => {
		ipcRenderer.invoke('compress', options)
	},
	// 提取音频
	extractAudio: (options: ExtractAudioOptions) => {
		return ipcRenderer.invoke('extractAudio', options)
	},
	//选择文件
	selectFile: (fileType: 'video' | 'image' | 'all' = 'all', multiple: boolean = true) => {
		return ipcRenderer.invoke('selectFile', fileType, multiple)
	},
	//选择目录
	selectDirectory: () => {
		return ipcRenderer.invoke('selectDirectory')
	},
	ts2mp4: () => {
		return ipcRenderer.invoke('ts2mp4')
	},
	// 获取视频信息
	getVideoInfo: (filePath: string) => {
		return ipcRenderer.invoke('getVideoInfo', filePath)
	},
	// 打开文件所在目录
	openFileDirectory: (filePath: string) => {
		return ipcRenderer.invoke('openFileDirectory', filePath)
	},
	// 处理拖放文件，通过主进程获取文件路径
	handleDragDropFiles: (fileList: any[]) => {
		return ipcRenderer.invoke('handle-drag-drop-files', fileList)
	},
	// 停止所有处理
	stop() {
		ipcRenderer.send('stop')
	},

	// 新增功能 - 视频处理
	// 视频格式转换
	convertVideoFormat: (options: VideoConvertOptions) => {
		return ipcRenderer.invoke('convertVideoFormat', options)
	},
	// 视频压缩（新版）
	compressVideo: (options: VideoCompressOptions) => {
		return ipcRenderer.invoke('compressVideo', options)
	},
	// 视频截帧
	extractFrames: (options: ExtractFramesOptions) => {
		return ipcRenderer.invoke('extractFrames', options)
	},

	// 新增功能 - 图片处理
	// 图片压缩
	compressImage: (options: ImageCompressOptions) => {
		return ipcRenderer.invoke('compressImage', options)
	},
	// 图片格式转换
	convertImageFormat: (options: ImageConvertOptions) => {
		return ipcRenderer.invoke('convertImageFormat', options)
	},
	// 图片清晰度提升
	enhanceImage: (options: ImageEnhanceOptions) => {
		return ipcRenderer.invoke('enhanceImage', options)
	},

	// 进程通知
	mainProcessNotice: (callback: (type: MainProcessNoticeType, data: any, path: string) => void) => {
		const listener = (_event: IpcRendererEvent, type: MainProcessNoticeType, data: any, path: string) => {
			callback(type, data, path)
		}

		ipcRenderer.on('mainProcessNotice', listener)

		// 返回一个取消订阅的函数
		return () => {
			ipcRenderer.removeListener('mainProcessNotice', listener)
		}
	},

	handleFiles: (callback) => {
		const handleDrop = (e) => {
			e.preventDefault();
			e.stopPropagation();

			// // 获取基本文件信息
			// const files = Array.from(e.dataTransfer.files).map(file => ({
			//   name: file.name,
			//   type: file.type,
			//   size: file.size
			// }));

			// // 发送到主进程处理
			// ipcRenderer.send('handle-files', files);


			// 获取拖拽项的原始数据
			const items = e.dataTransfer.items;
			if (items) {
				const entries = Array.from(items)
					.filter(item => item.kind === 'file')
					.map(item => item.webkitGetAsEntry());

				console.log("🚀 ~ handleDrop ~ entries:", entries)

				// 发送文件项到主进程
				// ipcRenderer.send('handle-file-entries', entries);
				ipcRenderer.send('handle-file-entries', entries.map(entry => ({
					name: entry.name,
					isFile: entry.isFile,
					isDirectory: entry.isDirectory,
					fullPath: entry.fullPath
				})));
			}
		};
		// 接收主进程返回的文件路径
		ipcRenderer.on('files-path-ready', (event, filePaths) => {
			callback(filePaths);
		});


		// 确保所有必要的事件都被处理
		document.addEventListener('dragenter', (e) => {
			e.preventDefault();
			e.stopPropagation();
		}, false);

		document.addEventListener('dragover', (e) => {
			e.preventDefault();
			e.stopPropagation();
			e.dataTransfer.dropEffect = 'copy'; // 显示复制图标
		}, false);

		document.addEventListener('dragleave', (e) => {
			e.preventDefault();
			e.stopPropagation();
		}, false);

		document.addEventListener('drop', handleDrop, false);
	}
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
console.log("🚀 ~ process.contextIsolated:", process.contextIsolated)
if (process.contextIsolated) {
	try {
		contextBridge.exposeInMainWorld('electron', electronAPI)
		contextBridge.exposeInMainWorld('api', api)
	} catch (error) {
		console.error(error)
	}
} else {
	// @ts-ignore (define in dts)
	window.electron = electronAPI
	// @ts-ignore (define in dts)
	window.api = api
}

console.log("🚀 ~ window:", window)

// 拦截全局拖拽，防止页面默认打开文件
window.addEventListener('dragover', e => e.preventDefault());
// TODO:
// window.addEventListener('drop', e => {
//   console.log("🚀 ~ files ~ e:", e)
//   console.log("🚀 ~ files ~ e.dataTransfer.files:", e.dataTransfer.files)
//   e.preventDefault();

//   // 这里拿到的每个 File 对象都含 .path
//   const files = Array.from(e.dataTransfer.files).map(f => ({
//     path: f.path,     // 真正的本地路径，只在主进程用
//     name: f.name,
//     type: f.type,
//     size: f.size
//   }));
//   console.log("🚀 ~ files ~ files:", files)

// });
