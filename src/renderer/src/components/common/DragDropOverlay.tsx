import { useState, useEffect } from 'react'
import { Upload } from 'lucide-react'
import { VIDEO_EXTENSIONS, IMAGE_EXTENSIONS } from '@renderer/utils/fileUtils'

interface DragDropOverlayProps {
	onFileDrop: (files: string[]) => void
	acceptFileTypes?: string
	multiple?: boolean
}

export function DragDropOverlay({ onFileDrop, acceptFileTypes = '*', multiple = true }: DragDropOverlayProps) {
	const [isDragging, setIsDragging] = useState(false)

	// 监听拖放事件
	useEffect(() => {
		const handleDragEnter = (e: DragEvent) => {
			e.preventDefault()
			e.stopPropagation()
			setIsDragging(true)
		}

		const handleDragOver = (e: DragEvent) => {
			e.preventDefault()
			e.stopPropagation()
		}

		const handleDragLeave = (e: DragEvent) => {
			e.preventDefault()
			e.stopPropagation()

			// 只有当拖动离开整个文档时才设置为false
			if (
				e.relatedTarget === null ||
				(e.relatedTarget as Node).nodeName === 'HTML'
			) {
				setIsDragging(false)
			}
		}

		const handleDrop = async (e: DragEvent) => {
			e.preventDefault()
			e.stopPropagation()
			setIsDragging(false)

			if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
				try {
					// 使用新的 Electron API 获取文件路径
					const files = Array.from(e.dataTransfer.files);
					const filePaths: string[] = [];

					for (const file of files) {
						try {
							// 使用 webUtils.getPathForFile 获取文件的绝对路径
							const filePath = window.api.getPathForFile(file);
							console.log('DragDropOverlay 获取到的文件路径:', filePath);
							if (filePath && typeof filePath === 'string') {
								filePaths.push(filePath);
							}
						} catch (error) {
							console.warn('获取文件路径时出错:', file.name, error);
						}
					}

					// 根据acceptFileTypes过滤文件
					let filteredPaths = filePaths;
					if (acceptFileTypes === 'video/*') {
						// 过滤视频文件
						filteredPaths = filePaths.filter(path => {
							const ext = path.split('.').pop()?.toLowerCase() || '';
							return VIDEO_EXTENSIONS.includes(ext);
						});
					} else if (acceptFileTypes === 'image/*') {
						// 过滤图片文件
						filteredPaths = filePaths.filter(path => {
							const ext = path.split('.').pop()?.toLowerCase() || '';
							return IMAGE_EXTENSIONS.includes(ext);
						});
					}

					// 调用回调函数处理文件路径
					if (filteredPaths.length > 0) {
						// 如果是单选模式，只使用第一个文件
						if (!multiple) {
							onFileDrop([filteredPaths[0]]);
						} else {
							onFileDrop(filteredPaths);
						}
					}
				} catch (error) {
					console.error('拖放文件处理出错:', error);
				}
			}
		}

		// 添加全局事件监听器
		document.addEventListener('dragenter', handleDragEnter)
		document.addEventListener('dragover', handleDragOver)
		document.addEventListener('dragleave', handleDragLeave)
		document.addEventListener('drop', handleDrop)

		// 清理函数
		return () => {
			document.removeEventListener('dragenter', handleDragEnter)
			document.removeEventListener('dragover', handleDragOver)
			document.removeEventListener('dragleave', handleDragLeave)
			document.removeEventListener('drop', handleDrop)
		}
	}, [onFileDrop, acceptFileTypes, multiple])

	if (!isDragging) return null

	return (
		<div className="absolute inset-0 z-50 flex items-center justify-center opacity-85">
			<div className="w-full h-full border-4 border-dashed border-[#1890FF] rounded-lg bg-[#E6F7FF] flex flex-col items-center justify-center">
				<Upload className="w-24 h-24 text-[#1890FF] mb-4" />
				<p className="text-[#262626] text-xl font-medium">释放鼠标以上传文件</p>
				<p className="text-[#595959] mt-2">
					{acceptFileTypes === 'video/*'
						? `支持的视频格式：${VIDEO_EXTENSIONS.map(ext => ext.toUpperCase()).join(', ')}`
						: acceptFileTypes === 'image/*'
							? `支持的图片格式：${IMAGE_EXTENSIONS.map(ext => ext.toUpperCase()).join(', ')}`
							: '拖放文件到此处'}
				</p>
			</div>
		</div>
	)
}
