import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@renderer/components/ui/card'
import { Loader2, AlertCircle, FileVideo, Music, Info, Upload, RefreshCw, Folder, Trash2 } from 'lucide-react'
import { Button } from '@renderer/components/ui/button'
import { cn } from '@renderer/lib/utils'
import { toast } from 'sonner'
import {
	getFileName,
	formatBitrate,
	formatDuration,
	formatFileSize,
	formatFrameRate,
	formatCodecName
} from '@renderer/utils/fileUtils'

interface VideoMetadata {
	format: {
		filename: string;
		duration: string;
		size: string;
		bit_rate: string;
		format_name?: string;
		format_long_name?: string;
		start_time?: string;
		nb_streams?: number;
		tags?: Record<string, string>;
	};
	streams: {
		codec_type: string;
		codec_name: string;
		codec_long_name?: string;
		codec_tag_string?: string;
		width?: number;
		height?: number;
		coded_width?: number;
		coded_height?: number;
		display_aspect_ratio?: string;
		sample_aspect_ratio?: string;
		pix_fmt?: string;
		level?: number;
		color_range?: string;
		color_space?: string;
		color_transfer?: string;
		color_primaries?: string;
		chroma_location?: string;
		field_order?: string;
		r_frame_rate?: string;
		avg_frame_rate?: string;
		time_base?: string;
		bit_rate?: string;
		max_bit_rate?: string;
		bits_per_raw_sample?: string;
		nb_frames?: string;
		channels?: number;
		channel_layout?: string;
		sample_rate?: string;
		sample_fmt?: string;
		profile?: string;
		disposition?: Record<string, number>;
		tags?: Record<string, string>;
	}[];
}

interface VideoInfoDisplayProps {
	videoInfo: VideoMetadata | null;
	isLoading: boolean;
	error: string | null;
	selectedFile: string | null;
	onSelectFile?: () => void;
	onGetInfo?: () => void;
	onClear?: () => void;
}

export function VideoInfoDisplay({
	videoInfo,
	isLoading,
	error,
	selectedFile,
	onSelectFile,
	onGetInfo,
	onClear
}: VideoInfoDisplayProps) {
	// 处理拖放功能
	const [isDragging, setIsDragging] = useState(false);

	const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		setIsDragging(true);
	};

	const handleDragLeave = () => {
		setIsDragging(false);
	};

	const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		setIsDragging(false);

		try {
			// 获取拖拽的文件
			const files = Array.from(e.dataTransfer.files);

			if (files.length === 0) {
				toast.warning('没有检测到文件');
				return;
			}

			// 过滤出视频文件
			const videoExtensions = ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', 'ts', 'mts', 'm2ts', 'wmv', 'asf', '3gp', 'm4v'];
			const videoFiles = files.filter(file => {
				const ext = file.name.split('.').pop()?.toLowerCase();
				return ext && videoExtensions.includes(ext);
			});

			if (videoFiles.length === 0) {
				const fileNames = files.map(f => f.name).join(', ');
				toast.error(`不支持的文件格式: ${fileNames}。请选择视频文件。`);
				return;
			}

			// 使用新的 Electron API 获取文件路径
			const filePaths: string[] = [];

			for (const file of videoFiles) {
				try {
					console.log('处理文件:', file.name, file);
					// 使用 webUtils.getPathForFile 获取文件的绝对路径
					const filePath = window.api.getPathForFile(file);
					console.log('获取到的文件路径:', filePath);
					if (filePath && typeof filePath === 'string') {
						filePaths.push(filePath);
					} else {
						console.warn('无法获取文件路径:', file.name);
					}
				} catch (error) {
					console.warn('获取文件路径时出错:', file.name, error);
				}
			}

			if (filePaths.length === 0) {
				toast.error('无法获取文件路径，请尝试使用文件选择按钮');
				return;
			}

			// 导入 useVideoStore
			const { useVideoStore } = await import('@renderer/store/videoStore');
			const { clearFiles, addFiles } = useVideoStore.getState();

			// 清空现有文件并添加新的拖拽文件（视频信息页面只支持单文件）
			clearFiles();
			addFiles(filePaths);

			// 显示成功提示
			const fileName = videoFiles[0].name;
			toast.success(`已选择视频文件: ${fileName}`);

		} catch (error) {
			console.error('处理拖拽文件时出错:', error);
			toast.error('处理拖拽文件时出错，请重试');
		}
	};

	// 打开文件所在目录
	const handleOpenDirectory = () => {
		if (selectedFile) {
			window.api.openFileDirectory(selectedFile);
		}
	};

	if (!selectedFile) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="text-base font-medium flex items-center">
						<Info className="h-4 w-4 mr-2 text-[#1890FF]" />
						视频信息
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div
						className={cn(
							"border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors",
							isDragging
								? "border-[#1890FF] bg-[#E6F7FF]"
								: "hover:border-[#1890FF] hover:bg-[#E6F7FF]"
						)}
						onClick={onSelectFile}
						onDragOver={handleDragOver}
						onDragLeave={handleDragLeave}
						onDrop={handleDrop}
					>
						<div className="flex flex-col items-center py-8">
							<Upload className="w-12 h-12 text-[#1890FF] mb-4" />
							<p className="text-[#595959] mb-4">
								{isDragging
									? "释放鼠标以上传文件"
									: "点击选择视频文件或拖拽文件到此区域"
								}
							</p>
							<Button variant="default" size="sm">
								选择视频文件
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (isLoading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="text-base font-medium flex items-center justify-between">
						<div className="flex items-center">
							<Info className="h-4 w-4 mr-2 text-[#1890FF]" />
							视频信息
						</div>
						<div className="flex items-center space-x-2">
							<Button
								variant="outline"
								size="sm"
								onClick={onClear}
								className="h-8"
								title="清空视频信息"
							>
								<Trash2 className="h-4 w-4 mr-2" />
								清空
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={onSelectFile}
								className="h-8"
							>
								<RefreshCw className="h-4 w-4 mr-2" />
								重新选择文件
							</Button>
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					{/* 文件路径显示 */}
					<div className="mb-4 p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
						<p className="text-[#595959] truncate flex-1">{selectedFile}</p>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleOpenDirectory}
							className="ml-2 p-1 text-[#8C8C8C] hover:text-[#1890FF] hover:bg-[#E6F7FF] rounded-full"
							title="打开文件所在目录"
						>
							<Folder className="w-4 h-4" />
						</Button>
					</div>

					<div className="flex flex-col items-center justify-center h-64">
						<Loader2 className="h-8 w-8 text-[#1890FF] animate-spin mb-4" />
						<p className="text-[#8C8C8C]">正在获取视频信息...</p>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="text-base font-medium flex items-center justify-between">
						<div className="flex items-center">
							<Info className="h-4 w-4 mr-2 text-[#1890FF]" />
							视频信息
						</div>
						<div className="flex items-center space-x-2">
							<Button
								variant="outline"
								size="sm"
								onClick={onClear}
								className="h-8"
								title="清空视频信息"
							>
								<Trash2 className="h-4 w-4 mr-2" />
								清空
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={onSelectFile}
								className="h-8"
							>
								<RefreshCw className="h-4 w-4 mr-2" />
								重新选择文件
							</Button>
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					{/* 文件路径显示 */}
					<div className="mb-4 p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
						<p className="text-[#595959] truncate flex-1">{selectedFile}</p>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleOpenDirectory}
							className="ml-2 p-1 text-[#8C8C8C] hover:text-[#1890FF] hover:bg-[#E6F7FF] rounded-full"
							title="打开文件所在目录"
						>
							<Folder className="w-4 h-4" />
						</Button>
					</div>

					<div className="flex flex-col items-center justify-center h-64">
						<AlertCircle className="h-8 w-8 text-[#F5222D] mb-4" />
						<p className="text-[#F5222D]">{error}</p>
						<Button
							variant="outline"
							onClick={onGetInfo}
							className="mt-4"
						>
							重试
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	if (!videoInfo) {
		return (
			<Card>
				<CardHeader>
					<CardTitle className="text-base font-medium flex items-center justify-between">
						<div className="flex items-center">
							<Info className="h-4 w-4 mr-2 text-[#1890FF]" />
							视频信息
						</div>
						<div className="flex items-center space-x-2">
							<Button
								variant="outline"
								size="sm"
								onClick={onClear}
								className="h-8"
								title="清空视频信息"
							>
								<Trash2 className="h-4 w-4 mr-2" />
								清空
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={onSelectFile}
								className="h-8"
							>
								<RefreshCw className="h-4 w-4 mr-2" />
								重新选择文件
							</Button>
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{/* 文件路径显示 */}
						<div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
							<p className="text-[#595959] truncate flex-1">{selectedFile}</p>
							<Button
								variant="ghost"
								size="icon"
								onClick={handleOpenDirectory}
								className="ml-2 p-1 text-[#8C8C8C] hover:text-[#1890FF] hover:bg-[#E6F7FF] rounded-full"
								title="打开文件所在目录"
							>
								<Folder className="w-4 h-4" />
							</Button>
						</div>

						<div className="flex flex-col items-center justify-center py-4 text-[#8C8C8C]">
							<p className="mb-4">点击下方按钮获取视频详细信息</p>
							<Button
								onClick={onGetInfo}
								className="w-full"
							>
								<Info className="mr-2 h-4 w-4" />
								获取视频信息
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}

	// 提取视频流和音频流
	const videoStream = videoInfo.streams.find(stream => stream.codec_type === 'video');
	const audioStream = videoInfo.streams.find(stream => stream.codec_type === 'audio');

	// 格式化视频信息
	const formattedDuration = formatDuration(videoInfo.format.duration);
	const formattedSize = formatFileSize(videoInfo.format.size);
	const formattedBitrate = formatBitrate(videoInfo.format.bit_rate);
	const containerFormat = videoInfo.format.format_long_name || videoInfo.format.format_name || '未知';

	return (
		<Card>
			<CardHeader>
				<CardTitle className="text-base font-medium flex items-center justify-between">
					<div className="flex items-center">
						<Info className="h-4 w-4 mr-2 text-[#1890FF]" />
						视频信息
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={onClear}
							className="h-8"
							title="清空视频信息"
						>
							<Trash2 className="h-4 w-4 mr-2" />
							清空
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={onSelectFile}
							className="h-8"
						>
							<RefreshCw className="h-4 w-4 mr-2" />
							重新选择文件
						</Button>
					</div>
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-6">
					{/* 文件路径显示 */}
					<div className="p-3 bg-[#F0F2F5] rounded-lg flex items-center justify-between">
						<p className="text-[#595959] truncate flex-1">{selectedFile}</p>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleOpenDirectory}
							className="ml-2 p-1 text-[#8C8C8C] hover:text-[#1890FF] hover:bg-[#E6F7FF] rounded-full"
							title="打开文件所在目录"
						>
							<Folder className="w-4 h-4" />
						</Button>
					</div>

					{/* 基本信息 */}
					<div>
						<h3 className="text-sm font-medium text-[#262626] mb-2 flex items-center">
							<FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
							基本信息
						</h3>
						<div className="grid sm:grid-cols-2 md:sm:grid-cols-4 gap-2">
							<div className="space-y-1">
								<p className="text-xs text-[#8C8C8C]">文件名</p>
								<p className="text-sm text-[#262626] truncate">{getFileName(videoInfo.format.filename)}</p>
							</div>
							<div className="space-y-1">
								<p className="text-xs text-[#8C8C8C]">容器格式</p>
								<p className="text-sm text-[#262626]">{containerFormat}</p>
							</div>
							<div className="space-y-1">
								<p className="text-xs text-[#8C8C8C]">时长</p>
								<p className="text-sm text-[#262626]">{formattedDuration}</p>
							</div>
							<div className="space-y-1">
								<p className="text-xs text-[#8C8C8C]">文件大小</p>
								<p className="text-sm text-[#262626]">{formattedSize}</p>
							</div>
							<div className="space-y-1">
								<p className="text-xs text-[#8C8C8C]">总比特率</p>
								<p className="text-sm text-[#262626]">{formattedBitrate}</p>
							</div>
							<div className="space-y-1">
								<p className="text-xs text-[#8C8C8C]">流数量</p>
								<p className="text-sm text-[#262626]">{videoInfo.format.nb_streams || videoInfo.streams.length}</p>
							</div>
							{videoInfo.format.tags?.title && (
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">标题</p>
									<p className="text-sm text-[#262626]">{videoInfo.format.tags.title}</p>
								</div>
							)}
							{videoInfo.format.tags?.artist || videoInfo.format.tags?.album_artist || videoInfo.format.tags?.composer && (
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">艺术家</p>
									<p className="text-sm text-[#262626]">
										{videoInfo.format.tags?.artist || videoInfo.format.tags?.album_artist || videoInfo.format.tags?.composer}
									</p>
								</div>
							)}
						</div>
					</div>

					{/* 视频流信息 */}
					{videoStream && (
						<div>
							<h3 className="text-sm font-medium text-[#262626] mb-2 flex items-center">
								<FileVideo className="h-4 w-4 mr-1 text-[#1890FF]" />
								视频流
							</h3>
							<div className="grid sm:grid-cols-2 md:sm:grid-cols-4 gap-2">
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">编码格式</p>
									<p className="text-sm text-[#262626]">{formatCodecName(videoStream.codec_name)}</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">分辨率</p>
									<p className="text-sm text-[#262626]">{videoStream.width} × {videoStream.height}</p>
								</div>
								{videoStream.coded_width && videoStream.coded_height && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">编码分辨率</p>
										<p className="text-sm text-[#262626]">{videoStream.coded_width} × {videoStream.coded_height}</p>
									</div>
								)}
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">宽高比</p>
									<p className="text-sm text-[#262626]">{videoStream.display_aspect_ratio || '未知'}</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">帧率</p>
									<p className="text-sm text-[#262626]">{formatFrameRate(videoStream.r_frame_rate || '')}</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">平均帧率</p>
									<p className="text-sm text-[#262626]">{formatFrameRate(videoStream.avg_frame_rate || '')}</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">视频比特率</p>
									<p className="text-sm text-[#262626]">{formatBitrate(videoStream.bit_rate || '')}</p>
								</div>
								{videoStream.pix_fmt && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">像素格式</p>
										<p className="text-sm text-[#262626]">{videoStream.pix_fmt}</p>
									</div>
								)}
								{videoStream.profile && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">编码配置</p>
										<p className="text-sm text-[#262626]">{videoStream.profile}</p>
									</div>
								)}
								{videoStream.level && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">编码级别</p>
										<p className="text-sm text-[#262626]">{videoStream.level}</p>
									</div>
								)}
								{videoStream.color_space && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">色彩空间</p>
										<p className="text-sm text-[#262626]">{videoStream.color_space}</p>
									</div>
								)}
								{videoStream.color_range && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">色彩范围</p>
										<p className="text-sm text-[#262626]">{videoStream.color_range}</p>
									</div>
								)}
							</div>
						</div>
					)}

					{/* 音频流信息 */}
					{audioStream && (
						<div>
							<h3 className="text-sm font-medium text-[#262626] mb-2 flex items-center">
								<Music className="h-4 w-4 mr-1 text-[#1890FF]" />
								音频流
							</h3>
							<div className="grid sm:grid-cols-2 md:sm:grid-cols-4 gap-2">
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">编码格式</p>
									<p className="text-sm text-[#262626]">{formatCodecName(audioStream.codec_name)}</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">声道数</p>
									<p className="text-sm text-[#262626]">{audioStream.channels} ({audioStream.channel_layout || '未知'})</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">采样率</p>
									<p className="text-sm text-[#262626]">{audioStream.sample_rate ? `${audioStream.sample_rate} Hz` : '未知'}</p>
								</div>
								<div className="space-y-1">
									<p className="text-xs text-[#8C8C8C]">音频比特率</p>
									<p className="text-sm text-[#262626]">{formatBitrate(audioStream.bit_rate || '')}</p>
								</div>
								{audioStream.sample_fmt && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">采样格式</p>
										<p className="text-sm text-[#262626]">{audioStream.sample_fmt}</p>
									</div>
								)}
								{audioStream.profile && (
									<div className="space-y-1">
										<p className="text-xs text-[#8C8C8C]">编码配置</p>
										<p className="text-sm text-[#262626]">{audioStream.profile}</p>
									</div>
								)}
							</div>
						</div>
					)}

					{/* 元数据标签 */}
					{(videoInfo.format.tags && Object.keys(videoInfo.format.tags).length > 0) && (
						<div>
							<h3 className="text-sm font-medium text-[#262626] mb-2 flex items-center">
								<Info className="h-4 w-4 mr-1 text-[#1890FF]" />
								元数据标签
							</h3>
							<div className="grid sm:grid-cols-2 md:sm:grid-cols-4 gap-2 max-h-40 overflow-y-auto">
								{Object.entries(videoInfo.format.tags)
									.filter(([key]) => !['title', 'artist', 'album_artist', 'composer'].includes(key))
									.map(([key, value]) => (
										<div key={key} className="space-y-1">
											<p className="text-xs text-[#8C8C8C]">{key}</p>
											<p className="text-sm text-[#262626] truncate">{value}</p>
										</div>
									))}
							</div>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
